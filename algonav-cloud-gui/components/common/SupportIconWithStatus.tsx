import React from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>on,
  Tooltip,
  Stack,
  Chip,
  Button,
  Box,
  CircularProgress
} from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import SupportIcon from './SupportIcon';
import { 
  useTicketStatus, 
  getTicketStatusColor, 
  getTicketStatusText 
} from '../../lib/hooks/useTicketsByTarget';

interface SupportIconWithStatusProps {
  targetType: 'job' | 'task' | 'dataset';
  targetId: number;
  onTicketClick: (ticket: any) => void;
  onCreateTicket: () => void;
  variant?: 'icon' | 'button'; // icon for TaskRow, button for JobHeader
  size?: 'small' | 'medium';
  className?: string;
}

const SupportIconWithStatus: React.FC<SupportIconWithStatusProps> = ({
  targetType,
  targetId,
  onTicketClick,
  onCreateTicket,
  variant = 'icon',
  size = 'small',
  className = ''
}) => {
  const { hasTicket, status, ticketId, isLoading } = useTicketStatus(targetType, targetId);

  const handleClick = () => {
    if (hasTicket && ticketId) {
      // Fetch the full ticket data and pass it to the click handler
      // For now, we'll pass a minimal ticket object with the ID
      onTicketClick({ id: ticketId, status });
    } else {
      onCreateTicket();
    }
  };

  const getIconColor = () => {
    if (isLoading) return 'text.secondary';
    if (!hasTicket) return 'text.secondary';
    
    switch (status) {
      case 'open':
        return 'warning.main';
      case 'in_progress':
        return 'info.main';
      case 'resolved':
        return 'success.main';
      case 'closed':
        return 'text.secondary';
      default:
        return 'text.secondary';
    }
  };

  const getTooltipText = () => {
    if (isLoading) return 'Loading ticket status...';
    if (!hasTicket) return `Request support for this ${targetType}`;
    return `View ticket (${getTicketStatusText(status)})`;
  };

  if (variant === 'button') {
    // Button variant for JobHeader
    return (
      <Stack direction="row" spacing={1} alignItems="center">
        <Button
          variant="outlined"
          color={hasTicket ? getTicketStatusColor(status) as any : 'primary'}
          size={size}
          startIcon={
            isLoading ? (
              <CircularProgress size={16} />
            ) : (
              <HelpOutlineIcon />
            )
          }
          onClick={handleClick}
          disabled={isLoading}
          sx={{
            color: getIconColor(),
            borderColor: getIconColor(),
            '&:hover': {
              borderColor: getIconColor(),
              backgroundColor: 'rgba(0, 0, 0, 0.04)'
            }
          }}
        >
          {hasTicket ? 'View Ticket' : 'Request Support'}
        </Button>
        
        {hasTicket && status && (
          <Chip
            label={getTicketStatusText(status)}
            color={getTicketStatusColor(status) as any}
            size="small"
            variant="outlined"
          />
        )}
      </Stack>
    );
  }

  // Icon variant for TaskRow
  return (
    <Stack direction="row" spacing={1} alignItems="center">
      <Tooltip title={getTooltipText()}>
        <span>
          <IconButton
            size={size}
            onClick={handleClick}
            disabled={isLoading}
            aria-label={getTooltipText()}
            sx={{
              color: getIconColor(),
              '&:hover': {
                color: hasTicket ? getIconColor() : 'primary.main'
              }
            }}
            className={className}
          >
            {isLoading ? (
              <CircularProgress size={20} />
            ) : variant === 'icon' && targetType === 'task' ? (
              <SupportIcon width={20} height={20} />
            ) : (
              <HelpOutlineIcon />
            )}
          </IconButton>
        </span>
      </Tooltip>
      
      {hasTicket && status && (
        <Chip
          label={getTicketStatusText(status)}
          color={getTicketStatusColor(status) as any}
          size="small"
          variant="outlined"
          sx={{ 
            fontSize: '0.75rem',
            height: '20px',
            '& .MuiChip-label': {
              px: 1
            }
          }}
        />
      )}
    </Stack>
  );
};

export default SupportIconWithStatus;
