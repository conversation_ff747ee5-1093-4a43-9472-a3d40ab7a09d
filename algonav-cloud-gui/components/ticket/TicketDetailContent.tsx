import React, { useState } from 'react';
import {
  Box,
  <PERSON>ack,
  <PERSON>po<PERSON>,
  <PERSON>,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Link as MuiLink
} from '@mui/material';
import { formatDistanceToNow } from 'date-fns';
import { useUpdateTicketStatus, useAddMessage } from '../../lib/hooks/useTickets';
import { useRouter } from 'next/navigation';
import { getTicketStatusText } from '../../lib/hooks/useTicketsByTarget';

interface TicketDetailContentProps {
  ticket: any;
  loading?: boolean;
  onStatusChange?: (ticketId: string, newStatus: string) => void;
  onAddMessage?: (ticketId: string, message: string) => void;
}

const TicketDetailContent: React.FC<TicketDetailContentProps> = ({
  ticket,
  loading = false,
  onStatusChange,
  onAddMessage
}) => {
  const router = useRouter();
  const [newMessage, setNewMessage] = useState('');
  const [isSubmittingMessage, setIsSubmittingMessage] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);

  const updateStatus = useUpdateTicketStatus();
  const addMessage = useAddMessage();

  // Helper functions for ticket actions
  const canCloseTicket = (status: string) => {
    return ['open', 'waiting_on_customer', 'resolved'].includes(status);
  };

  const canReopenTicket = (status: string) => {
    return ['closed'].includes(status);
  };

  const handleStatusChange = async (newStatus: string) => {
    if (!ticket || newStatus === ticket.status) return;

    setIsUpdatingStatus(true);
    try {
      await updateStatus.mutateAsync({
        id: ticket.id,
        status: newStatus
      });

      if (onStatusChange) {
        onStatusChange(ticket.id, newStatus);
      }
    } catch (error) {
      console.error('Failed to update status:', error);
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  const handleAddMessage = async () => {
    if (!ticket || !newMessage.trim()) return;

    setIsSubmittingMessage(true);
    try {
      await addMessage.mutateAsync({
        ticketId: ticket.id,
        body: newMessage.trim()
      });
      
      setNewMessage('');
      
      if (onAddMessage) {
        onAddMessage(ticket.id, newMessage.trim());
      }
    } catch (error) {
      console.error('Failed to add message:', error);
    } finally {
      setIsSubmittingMessage(false);
    }
  };

  const handleTargetClick = (target: any) => {
    if (target.target_type === 'job') {
      router.push(`/jobs/${target.target_id}`);
    } else if (target.target_type === 'task' && target.job_id) {
      router.push(`/jobs/${target.job_id}?task=${target.target_id}`);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'warning';
      case 'waiting_on_customer': return 'info';
      case 'resolved': return 'success';
      case 'closed': return 'default';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!ticket) {
    return (
      <Alert severity="error">
        Ticket not found or you don't have permission to view it.
      </Alert>
    );
  }

  return (
    <Stack spacing={3}>
      {/* Title and Description */}
      <Box>
        <Typography variant="h5" gutterBottom>
          {ticket.title}
        </Typography>
        {ticket.description && (
          <Typography variant="body1" color="text.secondary">
            {ticket.description}
          </Typography>
        )}
      </Box>

      {/* Status and Priority */}
      <Stack direction="row" spacing={2} alignItems="center">
        <Chip
          label={ticket.status.replace('_', ' ').toUpperCase()}
          color={getStatusColor(ticket.status) as any}
          size="small"
        />
        <Chip
          label={`Priority: ${ticket.priority.toUpperCase()}`}
          variant="outlined"
          size="small"
        />
        <Typography variant="body2" color="text.secondary">
          Created {formatDistanceToNow(new Date(ticket.created_at))} ago
        </Typography>
      </Stack>

      {/* Related Items */}
      {ticket.ticket_targets && ticket.ticket_targets.length > 0 && (
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Related Items
          </Typography>
          <Stack spacing={1}>
            {ticket.ticket_targets.map((target: any) => (
              <Card key={target.id} variant="outlined" sx={{ cursor: 'pointer' }}>
                <CardContent 
                  sx={{ py: 1, '&:last-child': { pb: 1 } }}
                  onClick={() => handleTargetClick(target)}
                >
                  <Stack direction="row" alignItems="center" spacing={2}>
                    <Chip
                      label={target.target_type.toUpperCase()}
                      size="small"
                      variant="outlined"
                    />
                    <MuiLink
                      component="span"
                      sx={{ 
                        cursor: 'pointer',
                        '&:hover': { textDecoration: 'underline' }
                      }}
                    >
                      {target.name || `${target.target_type} #${target.target_id}`}
                    </MuiLink>
                  </Stack>
                </CardContent>
              </Card>
            ))}
          </Stack>
        </Box>
      )}

      <Divider />

      {/* Status Display and Actions */}
      <Box>
        <Typography variant="subtitle2" gutterBottom>
          Ticket Status
        </Typography>
        <Stack direction="row" spacing={2} alignItems="center">
          <Chip
            label={getTicketStatusText(ticket.status)}
            color={getStatusColor(ticket.status) as any}
            size="medium"
            sx={{ fontWeight: 'medium' }}
          />

          {/* Contextual Action Buttons */}
          {canCloseTicket(ticket.status) && (
            <Button
              variant="outlined"
              size="small"
              onClick={() => handleStatusChange('closed')}
              disabled={isUpdatingStatus}
              sx={{ ml: 1 }}
            >
              {isUpdatingStatus ? <CircularProgress size={16} /> : 'Close Ticket'}
            </Button>
          )}

          {canReopenTicket(ticket.status) && (
            <Button
              variant="outlined"
              size="small"
              onClick={() => handleStatusChange('open')}
              disabled={isUpdatingStatus}
              sx={{ ml: 1 }}
            >
              {isUpdatingStatus ? <CircularProgress size={16} /> : 'Reopen Ticket'}
            </Button>
          )}
        </Stack>
      </Box>

      <Divider />

      {/* Messages */}
      <Box>
        <Typography variant="subtitle2" gutterBottom>
          Messages
        </Typography>
        
        {ticket.ticket_messages && ticket.ticket_messages.length > 0 ? (
          <Stack spacing={2}>
            {ticket.ticket_messages
              .sort((a: any, b: any) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())
              .map((message: any) => (
                <Card key={message.id} variant="outlined">
                  <CardContent sx={{ py: 2 }}>
                    <Typography variant="body2" gutterBottom>
                      {message.body}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {formatDistanceToNow(new Date(message.created_at))} ago
                    </Typography>
                  </CardContent>
                </Card>
              ))}
          </Stack>
        ) : (
          <Typography variant="body2" color="text.secondary">
            No messages yet.
          </Typography>
        )}
      </Box>

      {/* Add Message */}
      <Box>
        <Typography variant="subtitle2" gutterBottom>
          Add Message
        </Typography>
        <Stack spacing={2}>
          <TextField
            multiline
            rows={3}
            placeholder="Type your message here..."
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            fullWidth
          />
          <Button
            variant="contained"
            onClick={handleAddMessage}
            disabled={!newMessage.trim() || isSubmittingMessage}
            sx={{ alignSelf: 'flex-start' }}
          >
            {isSubmittingMessage ? <CircularProgress size={20} /> : 'Add Message'}
          </Button>
        </Stack>
      </Box>
    </Stack>
  );
};

export default TicketDetailContent;
