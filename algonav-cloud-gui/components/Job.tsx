"use client";
import React, { useState } from 'react'; // Removed useEffect import
import {
  Stack,
  Snackbar,
  Alert,
  Box,
  TextField,
  InputAdornment,
  IconButton
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import { useRouter } from 'next/navigation';

// Custom hooks
import { 
  useTasksState, 
  useDownloadState, 
  useSupportDialog, 
  useNotifications 
} from '../lib/hooks/useJobHooks';

// Components
import JobHeader from './job/JobHeader';
import JobMetadata from './job/JobMetadata';
import TasksTable from './job/TasksTable';
import SupportDialog from './job/SupportDialog';
import StatusFilter from './job/StatusFilter';
import MobileTaskCard from './job/MobileTaskCard';
import TicketDetailModal from './ticket/TicketDetailModal';

// Types
import { JobProps, Task } from '../types/job';

// Utils
import { getErrorMessage } from '../utils/errorUtils';

// Hooks
import { useTicket } from '../lib/hooks/useTickets';
import { useTicketsByTarget } from '../lib/hooks/useTicketsByTarget';

export default function Job({ job, tasks: initialTasks, highlightTaskId }: JobProps & { highlightTaskId?: number }) {
  const router = useRouter();

  // Ticket modal state
  const [ticketModalOpen, setTicketModalOpen] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState<any>(null);
  
  // Custom hooks for state management
  const {
    tasks,
    searchTerm,
    selectedStatuses,
    orderBy,
    order,
    selectedTaskIds,
    handleSort,
    handleSearch,
    handleStatusFilterChange,
    toggleTaskSelection,
    toggleAllTasksSelection,
    setTasks
  } = useTasksState(initialTasks);
  
  const {
    downloadingFiles,
    globalDownloading,
    handleDownloadFile,
    handleDownloadTaskFiles,
    handleDownloadAllOfType,
    handleDownloadAll,
    isDownloading
  } = useDownloadState(job?.id);
  
  const {
    supportDialogOpen,
    currentTaskId,
    supportReason,
    additionalInfo,
    supportType,
    datasetName,
    jobId,
    handleOpenSupportDialog,
    handleOpenJobSupportDialog,
    handleOpenGeneralSupportDialog,
    handleCloseSupportDialog,
    handleSupportReasonChange,
    handleAdditionalInfoChange,
    handleSubmitSupport
  } = useSupportDialog();
  
  const {
    snackbarOpen,
    snackbarMessage,
    snackbarSeverity,
    showNotification,
    handleCloseSnackbar
  } = useNotifications();

  // Removed redundant useEffect that was overwriting the filtered state
  // Handle support request submission
  const onSubmitSupport = async () => {
    try {
      const result = await handleSubmitSupport();
      if (result?.success) {
        showNotification(result.message, 'success');
      }
    } catch (error) {
      showNotification(getErrorMessage(error, 'Failed to submit support request'), 'error');
    }
  };

  // Handle search clear
  const handleClearSearch = () => {
    setTasks(initialTasks);
    // This is a workaround to clear the search input
    const event = {
      target: { value: '' }
    } as React.ChangeEvent<HTMLInputElement>;
    handleSearch(event);
  };

  // Higher-order function for error handling
  const createSafeFunction = <T extends Array<any>, R>(
    fn: (...args: T) => Promise<R>,
    errorMessage: string | ((...args: T) => string)
  ) => {
    return async (...args: T): Promise<R | undefined> => {
      try {
        return await fn(...args);
      } catch (error) {
        const message = typeof errorMessage === 'function'
          ? errorMessage(...args)
          : errorMessage;
        showNotification(getErrorMessage(error, message), 'error');
        return undefined;
      }
    };
  };
  
  // Error handling wrappers for download functions
  const safeDownloadFile = createSafeFunction(
    handleDownloadFile,
    'Failed to download file'
  );
  
  const safeDownloadTaskFiles = createSafeFunction(
    handleDownloadTaskFiles,
    'Failed to download task files'
  );
  
  const safeDownloadAllOfType = createSafeFunction(
    handleDownloadAllOfType,
    (type: string) => `Failed to download ${type} files`
  );
  
  const safeDownloadAll = createSafeFunction(
    handleDownloadAll,
    'Failed to download all files'
  );

  if (!job) {
    return <div>Job not found</div>;
  }

  return (
    <Stack spacing={3}>
      {/* Header with back button and status */}
      <JobHeader
        job={job}
        router={router}
        onOpenJobSupport={() => handleOpenJobSupportDialog(job.id)}
      />

      {/* Metadata Grid */}
      <JobMetadata job={job} tasks={tasks} />

      {/* Search and Filter Bar */}
      <Box sx={{ px: 2, mb: 2, display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, alignItems: { xs: 'flex-start', sm: 'center' }, gap: 2 }}>
        <TextField
          size="small"
          variant="outlined"
          placeholder="Search tasks and datasets..."
          value={searchTerm}
          onChange={handleSearch}
          sx={{ maxWidth: { xs: '100%', sm: '300px' }, width: '100%' }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon color="action" />
              </InputAdornment>
            ),
            endAdornment: searchTerm ? (
              <InputAdornment position="end">
                <IconButton
                  aria-label="clear search"
                  onClick={handleClearSearch}
                  edge="end"
                  size="small"
                >
                  <ClearIcon fontSize="small" />
                </IconButton>
              </InputAdornment>
            ) : null,
          }}
        />

        <StatusFilter 
          selectedStatuses={selectedStatuses}
          onChange={handleStatusFilterChange}
        />
      </Box>

      {/* Mobile View - Cards */}
      <Box sx={{ px: 2, display: { xs: 'block', md: 'none' } }}>
        {tasks.map((task) => (
          <MobileTaskCard
            key={task.id}
            task={task}
            onDownloadFile={safeDownloadFile}
            onDownloadTaskFiles={safeDownloadTaskFiles}
            onOpenSupportDialog={handleOpenSupportDialog}
            downloadingFiles={downloadingFiles}
            jobId={job.id}
          />
        ))}
      </Box>

      {/* Desktop View - Table */}
      <Box sx={{ display: { xs: 'none', md: 'block' } }}>
        <TasksTable
          tasks={tasks}
          orderBy={orderBy}
          order={order}
          selectedTaskIds={selectedTaskIds}
          downloadingFiles={downloadingFiles}
          onSort={handleSort}
          onToggleTaskSelection={toggleTaskSelection}
          onToggleAllTasksSelection={toggleAllTasksSelection}
          onDownloadFile={safeDownloadFile}
          onDownloadTaskFiles={safeDownloadTaskFiles}
          onDownloadAllOfType={safeDownloadAllOfType}
          onDownloadAll={safeDownloadAll}
          onOpenSupportDialog={handleOpenSupportDialog}
          jobId={job.id}
          job={job} // Pass the full job object
          highlightTaskId={highlightTaskId}
        />
      </Box>

      {/* Support Request Dialog */}
      <SupportDialog
        open={supportDialogOpen}
        taskId={currentTaskId}
        reason={supportReason}
        additionalInfo={additionalInfo}
        supportType={supportType}
        datasetName={datasetName}
        jobId={jobId}
        onClose={handleCloseSupportDialog}
        onReasonChange={handleSupportReasonChange}
        onAdditionalInfoChange={handleAdditionalInfoChange}
        onSubmit={onSubmitSupport}
      />

      {/* Notification Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleCloseSnackbar} 
          severity={snackbarSeverity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Stack>
  );
}
